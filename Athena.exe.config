<?xml version="1.0"?>
<configuration>
  <startup useLegacyV2RuntimeActivationPolicy="true">
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/>
  </startup>
  <connectionStrings>
    <add name="SqlConAgent" connectionString="Application Name=MH8Test;Data Source=192.168.35.189;Initial Catalog=MagicHand80_Agent;User ID=sa;Password=**************;"
      providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <add key="LocalSetting" value="LocalSetting.xml"/>
    <add key="BLL" value="MSC.Athena.BLL"/>
    <add key="RunType" value="Customer"/>
    <add key="EDITempMailFolder" value="C:\\EDITemp\Mails\"/>
    <add key="EDITempAttachFolder" value="C:\\EDITemp\Attach\"/>
    <add key="MHKHAttachFolder" value="C:\\EDITemp\MHKHAttach\"/>
    <add key="ExportEDIFile" value="ExportEDIFiles"/>
    <add key="RptBL" value="\Reports\Booking(sp_Rpt_GetBooking).rpt"/>
    <add key="RptBLRider" value="\Reports\BookingLowRider(sp_Rpt_GetBooking).rpt"/>
    <add key="RptCSTShippment" value="\Reports\CST-ShippingOrder(sp_Rpt_GetBooking).rpt"/>
    <add key="RptCSTBL" value="\Reports\CST-BolSample(sp_Rpt_GetBooking).rpt"/>
    <add key="RptDummy" value="\Reports\Dummy.rpt"/>
    <add key="Rpt" value="\Reports\"/>
    <add key="DownLoadFolder" value="\TempDownLoad"/>
    <add key="TempEmailFolder" value="\TempSendEmails"/>
  </appSettings>
  <system.serviceModel>
    <bindings>
      <netTcpBinding>
        <binding name="tcpBinding"
                 receiveTimeout="00:10:00"
                 sendTimeout="00:10:00"
                 openTimeout="00:01:00"
                 closeTimeout="00:01:00"
                 listenBacklog="100"
                 maxConnections="10"
                 maxBufferSize="2147483647"
                 maxReceivedMessageSize="2147483647">
          <readerQuotas maxStringContentLength="15000000"
                        maxArrayLength="2147483647"/>
          <security mode="None">
            <message clientCredentialType="None"/>
            <transport clientCredentialType="None"/>
          </security>
        </binding>
      </netTcpBinding>
    </bindings>
    <client>
      <endpoint name="SystemSettingBLL" address="net.tcp://localhost:12122/SystemSettingBLL" binding="netTcpBinding" bindingConfiguration="tcpBinding" contract="MSC.Athena.ServiceContracts.ISystemSetting"/>
      <endpoint name="FieldInfoBLL" address="net.tcp://localhost:12122/FieldInfoBLL" binding="netTcpBinding" bindingConfiguration="tcpBinding" contract="MSC.Athena.ServiceContracts.IFieldInfo"/>
      <endpoint name="RulesBLL" address="net.tcp://localhost:12122/RulesBLL" binding="netTcpBinding" bindingConfiguration="tcpBinding" contract="MSC.Athena.ServiceContracts.IRules"/>
      <endpoint name="ShipmentBLL" address="net.tcp://localhost:12122/ShipmentBLL" binding="netTcpBinding" bindingConfiguration="tcpBinding" contract="MSC.Athena.ServiceContracts.IShipment"/>
      <endpoint name="InfoCenterBLL" address="net.tcp://localhost:12122/InfoCenterBLL" binding="netTcpBinding" bindingConfiguration="tcpBinding" contract="MSC.Athena.ServiceContracts.IInfoCenter"/>
      <endpoint name="ReportBLL" address="net.tcp://localhost:12122/ReportBLL" binding="netTcpBinding" bindingConfiguration="tcpBinding" contract="MSC.Athena.ServiceContracts.IReport"/>
      <endpoint name="MHPermissionBLL" address="net.tcp://localhost:12122/MHPermissionBLL" binding="netTcpBinding" bindingConfiguration="tcpBinding" contract="MSC.Athena.ServiceContracts.IMHPermission"/>
    </client>
  </system.serviceModel>
</configuration>
