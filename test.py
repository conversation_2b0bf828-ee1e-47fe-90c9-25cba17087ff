import clr
clr.AddReference("C:\Program Files (x86)\MSC\Athena\Athena.exe")
# clr.AddReference("C:\Program Files (x86)\MSC\Athena\Athena.exe.config")
from MSC.Athena.WindowsApp.EDICenterModules import frmEDIInfoView

path = "C:/Users/<USER>/Desktop/MHEDI_MSC LIVORNO-FY513A-SI_20250514162009-冷箱.zip"
main_form = frmEDIInfoView(formName="导入EDI",isMultiShow=False, filePath = path)

# 调用公共方法
email_feedback_info = main_form.ExecuteMHEDI()

# 打印返回的结果
print(email_feedback_info)
# import clr
# clr.AddReference("C:\Program Files (x86)\MSC\Athena\Athena.exe")
# clr.AddReference("C:\Program Files (x86)\MSC\Athena\MSC.Athena.BLL.dll")
# from System import *
# from System.Collections.Generic import *
# from System.IO import *
# from System.Windows.Forms import *
# from MSC.Athena.WindowsApp.EDICenterModules import frmEDIInfoView
# from MSC.Athena.BLL.EDICenter.Request import XMLType
# from System.Configuration import ConfigurationManager
# from System.Data.SqlClient import SqlConnection
#
# # 加载程序集
# clr.AddReference('MSC.Athena.WindowsApp')
# clr.AddReference('DevExpress.XtraEditors')
# clr.AddReference('DevExpress.XtraGrid')
# clr.AddReference('DevExpress.XtraLayout')
# clr.AddReference('DevExpress.XtraSplashScreen')
# clr.AddReference('Microsoft.Office.Interop.Excel')
#
# # 创建窗体实例
# form_name = "Meta Data Viewer"
# is_multi_show = False
# file_path = r"path\to\your\edi_file.xml"  # 使用原始字符串避免转义问题
#
# try:
#     edi_info_view = frmEDIInfoView(form_name, is_multi_show, file_path)
#     edi_info_view.LoadFile(file_path)
# except Exception as e:
#     print(f"Error: {e}")
