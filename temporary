using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraLayout;
using DevExpress.XtraLayout.Utils;
using DevExpress.XtraSplashScreen;
using Microsoft.Office.Interop.Excel;
using MSC.Athena.BLL.EDICenter;
using MSC.Athena.BLL.EDICenter.Model;
using MSC.Athena.BLL.EDICenter.Request;
using MSC.Athena.Common;
using MSC.Athena.WindowsApp.Enums;
using MyOffice.Common.Data;
using MyOffice.MSC.ImportExport;
using MyOffice.MSC.Shipment;

namespace MSC.Athena.WindowsApp.EDICenterModules
{
	// Token: 0x02000247 RID: 583
	public class frmEDIInfoView : BaseForm
	{
		// Token: 0x0600188C RID: 6284 RVA: 0x0001066E File Offset: 0x0000E86E
		public frmEDIInfoView(string formName, bool isMultiShow) : base(formName, isMultiShow)
		{
			this.InitializeComponent();
			this.InitialControl();
			this._ediLogBll = new EDILogBLL();
			this._logWriteDisk = new LogWriteDisk();
			this.Text = formName;
		}

		// Token: 0x0600188D RID: 6285 RVA: 0x000106A1 File Offset: 0x0000E8A1
		public frmEDIInfoView(string formName, bool isMultiShow, string filePath) : this(formName, isMultiShow)
		{
			this.LoadFile(filePath);
			this._ediLogBll = new EDILogBLL();
		}

		// Token: 0x0600188E RID: 6286 RVA: 0x000EB724 File Offset: 0x000E9924
		private void InitialControl()
		{
			this.layoutControlItem3.Size = new Size(this.layoutControlItem3.Size.Width, base.Height);
			StyleFormatCondition styleFormatCondition = new StyleFormatCondition();
			StyleFormatCondition styleFormatCondition2 = new StyleFormatCondition();
			StyleFormatCondition styleFormatCondition3 = new StyleFormatCondition();
			styleFormatCondition.Appearance.BackColor = ConstVariable.FeedbackColorDictionary[FeedbackColerEnum.Error];
			styleFormatCondition.Appearance.Options.UseBackColor = true;
			styleFormatCondition.Condition = FormatConditionEnum.Expression;
			styleFormatCondition.Expression = "Upper([ResultLevel]) == 'ERROR'";
			styleFormatCondition2.Appearance.BackColor = ConstVariable.FeedbackColorDictionary[FeedbackColerEnum.Information];
			styleFormatCondition2.Appearance.Options.UseBackColor = true;
			styleFormatCondition2.Condition = FormatConditionEnum.Expression;
			styleFormatCondition2.Expression = "Upper([ResultLevel]) == 'INFORMATION'";
			styleFormatCondition3.Appearance.BackColor = ConstVariable.FeedbackColorDictionary[FeedbackColerEnum.Warning];
			styleFormatCondition3.Appearance.Options.UseBackColor = true;
			styleFormatCondition3.Condition = FormatConditionEnum.Expression;
			styleFormatCondition3.Expression = "Upper([ResultLevel]) == 'WARNING'";
			this.gvResult.FormatConditions.AddRange(new StyleFormatCondition[]
			{
				styleFormatCondition,
				styleFormatCondition2,
				styleFormatCondition3
			});
			new ToolTip
			{
				ShowAlways = true
			}.SetToolTip(this.gcMain, "You can drag and drop the xml or dtx file into the form.");
			this.DisplayActionButton(XMLType.UNKNOWN);
		}

		// Token: 0x0600188F RID: 6287 RVA: 0x000EB860 File Offset: 0x000E9A60
		public void LoadFile(string filePath)
		{
			base.SetStatusBarText(base.MFResource.Importing);
			try
			{
				if (this.CheckPath(ref filePath))
				{
					this.Reset();
					this.buttonEditFilePath.Text = filePath;
					this._tables = this.GetTablesByFile(this.buttonEditFilePath.Text);
					if (!this.IsDTX(this.buttonEditFilePath.Text))
					{
						this.DisplayActionButton(this.GetEDITypeByTables(this._tables));
					}
					else
					{
						this.DisplayActionButton(XMLType.MHEDI);
					}
					this.cbeTable.Properties.Items.AddRange(this._tables);
					base.SetStatusBarText(base.MFResource.ImportComplete);
				}
			}
			catch (Exception ex)
			{
				XtraMessageBox.Show(ex.Message, "MH8", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
		}

		// Token: 0x06001890 RID: 6288 RVA: 0x000EB938 File Offset: 0x000E9B38
		private void Reset()
		{
			this.cbeTable.SelectedIndex = -1;
			this.cbeTable.Properties.Items.Clear();
			this.gvMain.Columns.Clear();
			this.buttonEditFilePath.Text = string.Empty;
		}

		// Token: 0x06001891 RID: 6289 RVA: 0x000EB988 File Offset: 0x000E9B88
		private List<DataTable> GetDTXDataTables(string filePath)
		{
			List<Exception> list;
			MSCBookingCollection bookingCollection = MSCMHEDIHelper.LoadFromFile(filePath, out list);
			EDITables editables = new EDITables();
			editables.Converts(bookingCollection);
			List<DataTable> list2 = new List<DataTable>();
			foreach (KeyValuePair<string, DataTable> keyValuePair in editables.SPTables)
			{
				list2.Add(keyValuePair.Value);
			}
			return list2;
		}

		// Token: 0x06001892 RID: 6290 RVA: 0x000106BD File Offset: 0x0000E8BD
		private bool IsDTX(string filePath)
		{
			return Path.GetExtension(filePath).ToLower().Equals(".dtx");
		}

		// Token: 0x06001893 RID: 6291 RVA: 0x000106D9 File Offset: 0x0000E8D9
		private bool IsXML(string filePath)
		{
			return Path.GetExtension(filePath).ToLower().Equals(".xml");
		}

		// Token: 0x06001894 RID: 6292 RVA: 0x000106F5 File Offset: 0x0000E8F5
		private bool IsZIP(string filePath)
		{
			return Path.GetExtension(filePath).ToLower().Equals(".zip");
		}

		// Token: 0x06001895 RID: 6293 RVA: 0x000EBA00 File Offset: 0x000E9C00
		private bool CheckPath(ref string filePath)
		{
			if (string.IsNullOrEmpty(filePath))
			{
				XtraMessageBox.Show("The File path cannot be empty.", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				base.SetStatusBarText("");
				return false;
			}
			if (this.IsZIP(filePath))
			{
				string[] unZipFilePath = Utility.GetUnZipFilePath(filePath, FolderAssigner.AssignFuntionTempFolderPath(FunctionName.SaveMateDataViewerUnZip));
				if (unZipFilePath.Length > 1)
				{
					XtraMessageBox.Show("The zip file can contain only one file.", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Hand);
					Directory.Delete(FolderAssigner.AssignFuntionTempFolderPath(FunctionName.SaveMateDataViewerUnZip), true);
					base.SetStatusBarText("");
					return false;
				}
				filePath = unZipFilePath[0];
			}
			if (!this.IsDTX(filePath) && !this.IsXML(filePath))
			{
				XtraMessageBox.Show("only *.xml or *.dtx files can be chosen.", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				base.SetStatusBarText("");
				return false;
			}
			return true;
		}

		// Token: 0x06001896 RID: 6294 RVA: 0x000EBAC4 File Offset: 0x000E9CC4
		private XMLType GetEDITypeByTables(DataTableCollection tables)
		{
			if (tables == null)
			{
				throw new ArgumentNullException("tables");
			}
			if (!tables.Contains(frmEDIInfoView._Header))
			{
				return XMLType.UNKNOWN;
			}
			string key = tables[frmEDIInfoView._Header].Rows[0][frmEDIInfoView._EDIType].ToString().Trim();
			if (!CommonVariables.XMLTypes.ContainsKey(key))
			{
				return XMLType.UNKNOWN;
			}
			return CommonVariables.XMLTypes[key];
		}

		// Token: 0x06001897 RID: 6295 RVA: 0x000EBB38 File Offset: 0x000E9D38
		private DataTableCollection GetTablesByFile(string filePath)
		{
			if (string.IsNullOrEmpty(filePath))
			{
				throw new ArgumentNullException("filePath");
			}
			DataSet dataSet = new DataSet();
			if (this.IsDTX(filePath))
			{
				dataSet.Tables.AddRange(this.GetDTXDataTables(filePath).ToArray());
			}
			else
			{
				dataSet.ReadXml(filePath);
			}
			return dataSet.Tables;
		}

		// Token: 0x06001898 RID: 6296 RVA: 0x000EBB90 File Offset: 0x000E9D90
		private void DisplayActionButton(XMLType xmlType)
		{
			this.HideResultGrid();
			this.sbtnAction.Click -= this.sbtnAction_Click_Import;
			this.sbtnAction.Click -= this.sbtnAction_Click_UpdateStructure;
			this.sbtnAction.Click -= this.sbtnAction_Click_ExecuteMHEDI;
			switch (xmlType)
			{
			case XMLType.MHEDI:
				this.sbtnAction.Enabled = true;
				this.sbtnAction.Text = "Execute MHEDI";
				this.sbtnAction.Click += this.sbtnAction_Click_ExecuteMHEDI;
				this.layoutControlItem2.Visibility = LayoutVisibility.Always;
				return;
			case XMLType.MHKH:
				if (!this.IsMsc() && this.RegionCheck() && this.IsShowImportToDatabaseButton())
				{
					this.sbtnAction.Enabled = true;
					this.sbtnAction.Text = "Import to database";
					this.sbtnAction.Click += this.sbtnAction_Click_Import;
					this.layoutControlItem2.Visibility = LayoutVisibility.Always;
					return;
				}
				return;
			case XMLType.MHScript:
				if (this.IsShowMHUpgradeButton())
				{
					this.sbtnAction.Enabled = true;
					this.sbtnAction.Text = "MH Upgrade";
					this.sbtnAction.Click += this.sbtnAction_Click_UpdateStructure;
					this.layoutControlItem2.Visibility = LayoutVisibility.Always;
					base.Verify("ImportEDI - UpdateSchema");
					return;
				}
				return;
			case XMLType.MHVGMEDI:
				this.sbtnAction.Enabled = true;
				this.sbtnAction.Text = "Execute VGMEDI";
				this.sbtnAction.Click += this.sbtnAction_Click_ExecuteMHEDI;
				this.layoutControlItem2.Visibility = LayoutVisibility.Always;
				return;
			}
			this.sbtnAction.Enabled = false;
			this.sbtnAction.Text = string.Empty;
			this.layoutControlItem2.Visibility = LayoutVisibility.Never;
		}

		// Token: 0x06001899 RID: 6297 RVA: 0x000EBD68 File Offset: 0x000E9F68
		private bool IsShowMHUpgradeButton()
		{
			DataSet dataSet = new DataSet();
			dataSet.ReadXml(this.buttonEditFilePath.Text);
			if (dataSet.Tables.Count > 2)
			{
				try
				{
					if (dataSet.Tables.Contains("Header"))
					{
						dataSet.Tables.Remove("Header");
					}
					if (dataSet.Tables.Contains("Indexer"))
					{
						dataSet.Tables.Remove("Indexer");
					}
					int num = Convert.ToInt32(dataSet.Tables[0].TableName.Split(new string[]
					{
						"_"
					}, StringSplitOptions.RemoveEmptyEntries)[1].Replace(".", ""));
					int num2 = Convert.ToInt32(dataSet.Tables[dataSet.Tables.Count - 1].TableName.Split(new string[]
					{
						"_"
					}, StringSplitOptions.RemoveEmptyEntries)[2].Replace(".", ""));
					if (Convert.ToInt32(MHSystemInfo.Instance.DBVersion.Replace(".", "")) == num && num2 > num)
					{
						return true;
					}
				}
				catch (InvalidCastException)
				{
					XtraMessageBox.Show(string.Format("The version number is incorrect.", new object[0]), "MH8", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					return false;
				}
				catch (Exception ex)
				{
					XtraMessageBox.Show(ex.Message);
					return false;
				}
			}
			XtraMessageBox.Show("This is not a new upgrade package.", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
			return false;
		}

		// Token: 0x0600189A RID: 6298 RVA: 0x00010711 File Offset: 0x0000E911
		private bool IsShowImportToDatabaseButton()
		{
			DataSet dataSet = new DataSet();
			dataSet.ReadXml(this.buttonEditFilePath.Text);
			if (dataSet.Tables.Contains("AgentSystem"))
			{
				XtraMessageBox.Show("This is not the KH Feedback file, please resend MHKH request.", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				return false;
			}
			return true;
		}

		// Token: 0x0600189B RID: 6299 RVA: 0x000EBEF8 File Offset: 0x000EA0F8
		private bool RegionCheck()
		{
			DataSet dataSet = new DataSet();
			dataSet.ReadXml(this.buttonEditFilePath.Text);
			if (dataSet.Tables.Contains("AgentAPPModuleVersion"))
			{
				DataRow dataRow = dataSet.Tables["AgentAPPModuleVersion"].AsEnumerable().SingleOrDefault((DataRow v) => v.Field("APPModuleCode") == "MHLocation");
				if (dataRow == null)
				{
					XtraMessageBox.Show("This is an incorrect KH file.", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					return false;
				}
				if (dataRow.Field("APPModuleVersion") == MHSystemInfo.Instance.SYSServerLocation)
				{
					return true;
				}
				XtraMessageBox.Show(string.Format("This is a {0} file, current region is {1}.", (dataRow.Field("APPModuleVersion") == "N") ? "NPRC" : "SPRC", (MHSystemInfo.Instance.SYSServerLocation == "N") ? "NPRC" : "SPRC"), "MH8", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
			}
			else
			{
				XtraMessageBox.Show("This is an incorrect KH file.", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
			}
			return false;
		}

		// Token: 0x0600189C RID: 6300 RVA: 0x00010751 File Offset: 0x0000E951
		private bool IsMsc()
		{
			return LocalSetting.Instance.RunType == RunType.MSC;
		}

		// Token: 0x0600189D RID: 6301 RVA: 0x000EC018 File Offset: 0x000EA218
		private void sbtnAction_Click_ExecuteMHEDI(object sender, EventArgs e)
		{
			try
			{
				base.SetStatusBarText(base.MFResource.Importing);
				this.sbtnAction.Enabled = false;
				Func<EmailFeedbackInfo> func = new Func<EmailFeedbackInfo>(this.ExecuteMHEDI);
				func.BeginInvoke(delegate(IAsyncResult obj)
				{
					Func<EmailFeedbackInfo> func2 = obj.AsyncState as Func<EmailFeedbackInfo>;
					EmailFeedbackInfo feedbackInfo = func2.EndInvoke(obj);
					base.Invoke(new Action(delegate()
					{
						if (feedbackInfo.AllErrors.Count<MSC.Athena.BLL.EDICenter.ErrorInfo>() > 0)
						{
							this.gridControlResult.DataSource = feedbackInfo.AllErrors.ToList<MSC.Athena.BLL.EDICenter.ErrorInfo>();
						}
						else
						{
							this.gridControlResult.DataSource = feedbackInfo.RequestFeedBackInfos[0].DBReturnInfo.Tables[0];
						}
						this.ShowResultGrid();
						this.sbtnAction.Enabled = true;
						this.SetStatusBarText("");
					}));
				}, func);
			}
			catch (Exception ex)
			{
				XtraMessageBox.Show(LogWriteDisk.GetErrorFormatMessage("Execute MHEDI Error", ex), "MH8", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				base.Log("Execute MHEDI Error", ex);
			}
		}

		// Token: 0x0600189E RID: 6302 RVA: 0x00002BDE File Offset: 0x00000DDE
		private void sbtnAction_Click_ExecuteMHVGMEDI(object sender, EventArgs e)
		{
		}

		// Token: 0x0600189F RID: 6303 RVA: 0x000EC0A0 File Offset: 0x000EA2A0
		private void ShowResultGrid()
		{
			this.layoutControlItemResult.Visibility = LayoutVisibility.Always;
			this.layoutControlItemResult.Size = new Size(this.layoutControlItemResult.Size.Width, this.layoutControlGroup1.Height / 2);
			if (this.gvResult.Columns.Contains(this.gvResult.Columns["BookingId"]))
			{
				this.gvResult.Columns["BookingId"].Visible = false;
			}
			if (this.gvResult.Columns.Contains(this.gvResult.Columns["SubKeyId"]))
			{
				this.gvResult.Columns["SubKeyId"].Visible = false;
			}
			this.gvResult.BestFitColumns();
		}

		// Token: 0x060018A0 RID: 6304 RVA: 0x000EC178 File Offset: 0x000EA378
		private void HideResultGrid()
		{
			this.layoutControlItemResult.Visibility = LayoutVisibility.Never;
			this.layoutControlItem3.Size = new Size(this.layoutControlItem3.Size.Width, base.Height);
		}

		// Token: 0x060018A1 RID: 6305 RVA: 0x000EC1BC File Offset: 0x000EA3BC
		public EmailFeedbackInfo ExecuteMHEDI()
		{
			int ediLogId = -1;
			MHRequest mhrequest = null;
			string mailSubject = "Console import";
			EmailFeedbackInfo emailFeedbackInfo = new EmailFeedbackInfo(null);
			try
			{
				FileType fileType;
				RequestType requestType = MHRequest.AnalyseRequestType(this.buttonEditFilePath.Text, out fileType);
				switch (requestType)
				{
				case RequestType.None:
				case RequestType.KH:
				case RequestType.None | RequestType.KH:
					break;
				case RequestType.EDI:
					ediLogId = this._ediLogBll.WriteRecievedEmailLogV2(null, true, null, null, mailSubject, DateTime.Now, "Received", null, null, "MHEDI");
					mhrequest = new EDIRequest(this.buttonEditFilePath.Text, fileType, false);
					break;
				default:
					if (requestType != RequestType.VGMEDI)
					{
						if (requestType != RequestType.UNKNOW)
						{
						}
					}
					else
					{
						ediLogId = this._ediLogBll.WriteRecievedEmailLogV2(null, true, null, null, mailSubject, DateTime.Now, "Received", null, null, "MHVGMEDI");
						mhrequest = new VGMEDIRequest(this.buttonEditFilePath.Text, fileType);
					}
					break;
				}
			}
			catch (Exception ex)
			{
				emailFeedbackInfo.EmailErrors.Add(new MSC.Athena.BLL.EDICenter.ErrorInfo
				{
					Level = ErrorLevel.SystemError,
					Object = ErrorObject.Environment,
					AttachmentName = this.buttonEditFilePath.Text,
					ErrorMessage = ex.Message,
					ErrorTrace = ex.StackTrace
				});
			}
			try
			{
				bool flag;
				emailFeedbackInfo.RequestFeedBackInfos.Add(mhrequest.ExcecuteRequest(ediLogId, string.Empty, MHUserInfo.Instance.UserId, out flag));
			}
			catch (Exception ex2)
			{
				if (emailFeedbackInfo.RequestFeedBackInfos.Count <= 0)
				{
					emailFeedbackInfo.RequestFeedBackInfos.Add(new RequestFileFeedbackInfo());
				}
				emailFeedbackInfo.RequestFeedBackInfos[0].ErrorList.Add(new MSC.Athena.BLL.EDICenter.ErrorInfo
				{
					Level = (ErrorLevel.Error | ErrorLevel.SystemError),
					Object = ErrorObject.Attachment,
					AttachmentName = this.buttonEditFilePath.Text,
					ErrorTrace = ex2.StackTrace,
					ErrorMessage = ex2.Message
				});
			}
			return emailFeedbackInfo;
		}

		// Token: 0x060018A2 RID: 6306 RVA: 0x000EC390 File Offset: 0x000EA590
		private void UpdateStructure()
		{
			base.Enabled = false;
			WaitDialogForm waitDialogForm = new WaitDialogForm("Updating the structure...", "Update Structure");
			try
			{
				DBMaintenanceSetting setting = new DBMaintenanceSetting(this.buttonEditFilePath.Text, ConfigurationManager.ConnectionStrings[Utility.SQLSTRNAME].ConnectionString);
				MODbXMLImportInfo importInfo = new MODbXMLImportInfo
				{
					ImportOption = MODbXMLRecordType.moPreScript,
					ImportScriptLevelType = ((LocalSetting.Instance.RunType == RunType.MSC) ? MODbXMLScriptLevelType.moMSC : MODbXMLScriptLevelType.moAll)
				};
				if (DBMaintenance.ImportXML(setting, importInfo))
				{
					XtraMessageBox.Show("Updated successfully", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					if (XtraMessageBox.Show("Now need to restart Magic Hand 8.", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Asterisk) == DialogResult.OK)
					{
						System.Windows.Forms.Application.Restart();
					}
				}
				else
				{
					XtraMessageBox.Show("Update failed", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
			catch (Exception ex)
			{
				XtraMessageBox.Show(LogWriteDisk.GetErrorFormatMessage("Update the database structure is abnormal!", ex), "MH8", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				base.Log("Update the database structure is abnormal!", ex);
			}
			finally
			{
				waitDialogForm.Close();
				waitDialogForm.Dispose();
				waitDialogForm = null;
				base.Enabled = true;
			}
		}

		// Token: 0x060018A3 RID: 6307 RVA: 0x000EC4A8 File Offset: 0x000EA6A8
		private void BackupDatabase()
		{
			string arg = null;
			base.Enabled = false;
			WaitDialogForm waitDialogForm = new WaitDialogForm("Backing up database...", "Backup Database");
			try
			{
				if (DBMaintenance.BackUp(new SqlConnection(ConfigurationManager.ConnectionStrings[Utility.SQLSTRNAME].ConnectionString), out arg))
				{
					XtraMessageBox.Show(string.Format("The backup is completed.{0}{1}", Environment.NewLine, arg), "MH8", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
				else
				{
					XtraMessageBox.Show("The backup failed ", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
			catch
			{
				XtraMessageBox.Show("The backup failed ", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
			finally
			{
				waitDialogForm.Close();
				waitDialogForm.Dispose();
				waitDialogForm = null;
				base.Enabled = true;
			}
		}

		// Token: 0x060018A4 RID: 6308 RVA: 0x000044E0 File Offset: 0x000026E0
		protected override void OnLoad(EventArgs e)
		{
			if (this.waitForm != null)
			{
				SplashScreenManager.CloseForm(false);
				this.waitForm.Close();
			}
		}

		// Token: 0x060018A5 RID: 6309 RVA: 0x000EC570 File Offset: 0x000EA770
		private void sbtnExportToExcel_Click(object sender, EventArgs e)
		{
			if (this._tables == null || this._tables.Count == 0)
			{
				XtraMessageBox.Show("No record.", "Export", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				return;
			}
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.Filter = "Excel File|*.xls;";
			saveFileDialog.FileName = string.Format("ExportData_{0}.xls", DateTime.Now.ToString("yyyyMMddHHmm"));
			if (saveFileDialog.ShowDialog() == DialogResult.OK && saveFileDialog.FileName.Trim() != null)
			{
				if (this.ExportToExcelByKH(this._tables[0].DataSet, saveFileDialog.FileName))
				{
					XtraMessageBox.Show("Export Completed", "Export", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					return;
				}
				XtraMessageBox.Show("Export Failure ", "Export", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
		}

		// Token: 0x060018A6 RID: 6310 RVA: 0x000EC638 File Offset: 0x000EA838
		private bool ExportToExcelByKH(DataSet ds, string filePath)
		{
			DataSet dataSet = ds.Copy();
			if (dataSet.Tables.Contains("Header"))
			{
				dataSet.Tables.Remove("Header");
			}
			if (dataSet.Tables.Contains("Indexer"))
			{
				dataSet.Tables.Remove("Indexer");
			}
			if (dataSet.Tables.Contains("AgentAPPModuleVersion"))
			{
				dataSet.Tables.Remove("AgentAPPModuleVersion");
			}
			if (dataSet.Tables.Contains("APPModuleVersion"))
			{
				dataSet.Tables.Remove("APPModuleVersion");
			}
			return this.ExportToExcel(dataSet, filePath);
		}

		// Token: 0x060018A7 RID: 6311 RVA: 0x000EC6DC File Offset: 0x000EA8DC
		private bool ExportToExcel(DataSet ds, string filePath)
		{
			if (ds == null || ds.Tables.Count == 0)
			{
				return false;
			}
			bool result;
			try
			{
				Microsoft.Office.Interop.Excel.Application application = (Microsoft.Office.Interop.Excel.Application)Activator.CreateInstance(Type.GetTypeFromCLSID(new Guid("00024500-0000-0000-C000-000000000046")));
				Microsoft.Office.Interop.Excel._Workbook workbook = application.Workbooks.Add(true);
				object value = Missing.Value;
				for (int i = 0; i < ds.Tables.Count; i++)
				{
					(workbook.ActiveSheet as Microsoft.Office.Interop.Excel._Worksheet).Name = ds.Tables[i].TableName;
					for (int j = 0; j < ds.Tables[i].Columns.Count; j++)
					{
						application.Cells[1, j + 1] = ds.Tables[i].Columns[j].ColumnName;
					}
					for (int k = 0; k < ds.Tables[i].Rows.Count; k++)
					{
						for (int l = 0; l < ds.Tables[i].Columns.Count; l++)
						{
							application.Cells[k + 2, l + 1] = ds.Tables[i].Rows[k][l].ToString();
						}
					}
					if (i < ds.Tables.Count - 1)
					{
						application.Sheets.Add(value, value, 1, XlSheetType.xlWorksheet);
					}
				}
				application.Visible = true;
				workbook.SaveAs(filePath, value, value, value, value, value, Microsoft.Office.Interop.Excel.XlSaveAsAccessMode.xlShared, value, value, value, value, value);
				result = true;
			}
			catch (Exception ex)
			{
				XtraMessageBox.Show(ex.ToString(), "Magic Hand", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				result = false;
			}
			finally
			{
				GC.Collect();
			}
			return result;
		}

		// Token: 0x060018A8 RID: 6312 RVA: 0x000EC8FC File Offset: 0x000EAAFC
		private void buttonEditFilePath_ButtonClick(object sender, ButtonPressedEventArgs e)
		{
			try
			{
				base.SetStatusBarText(base.MFResource.Importing);
				if (this.openFileDialog.ShowDialog() == DialogResult.OK)
				{
					string fileName = this.openFileDialog.FileName;
					if (this.CheckPath(ref fileName))
					{
						this.Reset();
						this.buttonEditFilePath.Text = fileName;
						this._tables = this.GetTablesByFile(this.buttonEditFilePath.Text);
						if (!this.IsDTX(this.buttonEditFilePath.Text))
						{
							this.DisplayActionButton(this.GetEDITypeByTables(this._tables));
						}
						else
						{
							this.DisplayActionButton(XMLType.MHEDI);
						}
						this.cbeTable.Properties.Items.AddRange(this._tables);
						base.SetStatusBarText(base.MFResource.ImportComplete);
					}
				}
				else
				{
					base.SetStatusBarText("");
				}
			}
			catch (Exception ex)
			{
				XtraMessageBox.Show(ex.Message, "MH8", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
		}

		// Token: 0x060018A9 RID: 6313 RVA: 0x00010762 File Offset: 0x0000E962
		private void sbtnAction_Click_UpdateStructure(object sender, EventArgs e)
		{
			if (DialogResult.No == XtraMessageBox.Show("Do you want to execute this operation?", "MH8", MessageBoxButtons.YesNo, MessageBoxIcon.Question))
			{
				return;
			}
			this.UpdateStructure();
		}

		// Token: 0x060018AA RID: 6314 RVA: 0x000EC9FC File Offset: 0x000EABFC
		private void sbtnAction_Click_Import(object sender, EventArgs e)
		{
			if (DialogResult.No == XtraMessageBox.Show("Do you want to execute this operation?", "MH8", MessageBoxButtons.YesNo, MessageBoxIcon.Question))
			{
				return;
			}
			base.Enabled = false;
			try
			{
				MHKHHandleBase mhkhhandleBase = new MHKHReceiveEmailHandle();
				MHKHRequest request = new MHKHRequest(MHKHType.Client, new MHKHSetting
				{
					MSCEmailAddress = LocalSetting.Instance.GetSetting(LocalSetting.MSCEmailAddress).GetString(),
					GenerateXMLPath = LocalSetting.Instance.GetSetting(LocalSetting.sendAttachmentPath).GetString(),
					AttachmentPath = this.buttonEditFilePath.Text
				}, MHKHHandleType.Receive);
				mhkhhandleBase.Process(request, null);
				XtraMessageBox.Show("The import operation completed.", "MH8", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
			}
			catch (Exception ex)
			{
				XtraMessageBox.Show(LogWriteDisk.GetErrorFormatMessage("The import failed.", ex), "MH8", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				base.Log("The import failed.", ex);
			}
			finally
			{
				base.Enabled = true;
			}
		}

		// Token: 0x060018AB RID: 6315 RVA: 0x000ECAF0 File Offset: 0x000EACF0
		private void cbeTable_EditValueChanged(object sender, EventArgs e)
		{
			this.gvMain.Columns.Clear();
			ComboBoxEdit comboBoxEdit = sender as ComboBoxEdit;
			this.gcMain.DataSource = this._tables[comboBoxEdit.Text];
			this.gvMain.BestFitColumns();
		}

		// Token: 0x060018AC RID: 6316 RVA: 0x000057A3 File Offset: 0x000039A3
		private void frmEDIInfoView_DragEnter(object sender, DragEventArgs e)
		{
			if (e.Data.GetDataPresent(DataFormats.FileDrop))
			{
				e.Effect = DragDropEffects.Link;
				return;
			}
			e.Effect = DragDropEffects.None;
		}

		// Token: 0x060018AD RID: 6317 RVA: 0x000ECB3C File Offset: 0x000EAD3C
		private void frmEDIInfoView_DragDrop(object sender, DragEventArgs e)
		{
			try
			{
				string filePath = ((Array)e.Data.GetData(DataFormats.FileDrop)).GetValue(0).ToString();
				this.LoadFile(filePath);
			}
			catch (Exception ex)
			{
				XtraMessageBox.Show(ex.Message, "MH8", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
		}

		// Token: 0x060018AE RID: 6318 RVA: 0x00010780 File Offset: 0x0000E980
		private void frmEDIInfoView_FormClosed(object sender, FormClosedEventArgs e)
		{
			base.SetStatusBarText("");
		}

		// Token: 0x060018AF RID: 6319 RVA: 0x0001078D File Offset: 0x0000E98D
		protected override void Dispose(bool disposing)
		{
			if (disposing && this.components != null)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060018B0 RID: 6320 RVA: 0x000ECB98 File Offset: 0x000EAD98
		private void InitializeComponent()
		{
			SerializableAppearanceObject appearance = new SerializableAppearanceObject();
			this.openFileDialog = new OpenFileDialog();
			this.layoutControl1 = new LayoutControl();
			this.sbtnExportToExcel = new SimpleButton();
			this.gcMain = new GridControl();
			this.gvMain = new GridView();
			this.gridControlResult = new GridControl();
			this.gvResult = new GridView();
			this.sbtnAction = new SimpleButton();
			this.cbeTable = new ComboBoxEdit();
			this.buttonEditFilePath = new ButtonEdit();
			this.layoutControlGroup1 = new LayoutControlGroup();
			this.lciFilePath = new LayoutControlItem();
			this.lciTable = new LayoutControlItem();
			this.layoutControlItem2 = new LayoutControlItem();
			this.emptySpaceItem1 = new EmptySpaceItem();
			this.emptySpaceItem2 = new EmptySpaceItem();
			this.emptySpaceItem3 = new EmptySpaceItem();
			this.layoutControlItemResult = new LayoutControlItem();
			this.layoutControlItem3 = new LayoutControlItem();
			this.layoutControlItem1 = new LayoutControlItem();
			((ISupportInitialize)this.layoutControl1).BeginInit();
			this.layoutControl1.SuspendLayout();
			((ISupportInitialize)this.gcMain).BeginInit();
			((ISupportInitialize)this.gvMain).BeginInit();
			((ISupportInitialize)this.gridControlResult).BeginInit();
			((ISupportInitialize)this.gvResult).BeginInit();
			((ISupportInitialize)this.cbeTable.Properties).BeginInit();
			((ISupportInitialize)this.buttonEditFilePath.Properties).BeginInit();
			((ISupportInitialize)this.layoutControlGroup1).BeginInit();
			((ISupportInitialize)this.lciFilePath).BeginInit();
			((ISupportInitialize)this.lciTable).BeginInit();
			((ISupportInitialize)this.layoutControlItem2).BeginInit();
			((ISupportInitialize)this.emptySpaceItem1).BeginInit();
			((ISupportInitialize)this.emptySpaceItem2).BeginInit();
			((ISupportInitialize)this.emptySpaceItem3).BeginInit();
			((ISupportInitialize)this.layoutControlItemResult).BeginInit();
			((ISupportInitialize)this.layoutControlItem3).BeginInit();
			((ISupportInitialize)this.layoutControlItem1).BeginInit();
			base.SuspendLayout();
			this.openFileDialog.Filter = "(*.xml;*.dtx;*.zip)|*.xml;*.dtx;*.zip";
			this.layoutControl1.AllowCustomization = false;
			this.layoutControl1.Controls.Add(this.sbtnExportToExcel);
			this.layoutControl1.Controls.Add(this.gcMain);
			this.layoutControl1.Controls.Add(this.gridControlResult);
			this.layoutControl1.Controls.Add(this.sbtnAction);
			this.layoutControl1.Controls.Add(this.cbeTable);
			this.layoutControl1.Controls.Add(this.buttonEditFilePath);
			this.layoutControl1.Dock = DockStyle.Fill;
			this.layoutControl1.Location = new Point(0, 0);
			this.layoutControl1.Name = "layoutControl1";
			this.layoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new Rectangle?(new Rectangle(293, 336, 250, 350));
			this.layoutControl1.Root = this.layoutControlGroup1;
			this.layoutControl1.Size = new Size(988, 693);
			this.layoutControl1.TabIndex = 0;
			this.layoutControl1.Text = "layoutControl1";
			this.sbtnExportToExcel.Location = new Point(672, 31);
			this.sbtnExportToExcel.Name = "sbtnExportToExcel";
			this.sbtnExportToExcel.Size = new Size(84, 22);
			this.sbtnExportToExcel.StyleController = this.layoutControl1;
			this.sbtnExportToExcel.TabIndex = 6;
			this.sbtnExportToExcel.Text = "Export to Excel";
			this.sbtnExportToExcel.Click += this.sbtnExportToExcel_Click;
			this.gcMain.Location = new Point(7, 57);
			this.gcMain.MainView = this.gvMain;
			this.gcMain.Name = "gcMain";
			this.gcMain.Size = new Size(974, 363);
			this.gcMain.TabIndex = 5;
			this.gcMain.ViewCollection.AddRange(new BaseView[]
			{
				this.gvMain
			});
			this.gvMain.GridControl = this.gcMain;
			this.gvMain.Name = "gvMain";
			this.gvMain.OptionsBehavior.Editable = false;
			this.gvMain.OptionsView.ColumnAutoWidth = false;
			this.gvMain.OptionsView.ShowGroupPanel = false;
			this.gridControlResult.Location = new Point(10, 427);
			this.gridControlResult.MainView = this.gvResult;
			this.gridControlResult.Name = "gridControlResult";
			this.gridControlResult.Size = new Size(968, 230);
			this.gridControlResult.TabIndex = 3;
			this.gridControlResult.ViewCollection.AddRange(new BaseView[]
			{
				this.gvResult
			});
			this.gvResult.GridControl = this.gridControlResult;
			this.gvResult.Name = "gvResult";
			this.gvResult.OptionsBehavior.Editable = false;
			this.gvResult.OptionsSelection.MultiSelect = true;
			this.gvResult.OptionsView.ColumnAutoWidth = false;
			this.gvResult.OptionsView.ShowGroupPanel = false;
			this.sbtnAction.Location = new Point(881, 664);
			this.sbtnAction.MaximumSize = new Size(100, 22);
			this.sbtnAction.MinimumSize = new Size(100, 22);
			this.sbtnAction.Name = "sbtnAction";
			this.sbtnAction.Size = new Size(100, 22);
			this.sbtnAction.StyleController = this.layoutControl1;
			this.sbtnAction.TabIndex = 4;
			this.sbtnAction.Text = "Action";
			this.cbeTable.Location = new Point(36, 31);
			this.cbeTable.Name = "cbeTable";
			this.cbeTable.Properties.Buttons.AddRange(new EditorButton[]
			{
				new EditorButton(ButtonPredefines.Combo)
			});
			this.cbeTable.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;
			this.cbeTable.Size = new Size(632, 20);
			this.cbeTable.StyleController = this.layoutControl1;
			this.cbeTable.TabIndex = 1;
			this.cbeTable.EditValueChanged += this.cbeTable_EditValueChanged;
			this.buttonEditFilePath.Location = new Point(36, 7);
			this.buttonEditFilePath.Name = "buttonEditFilePath";
			this.buttonEditFilePath.Properties.Buttons.AddRange(new EditorButton[]
			{
				new EditorButton(ButtonPredefines.Glyph, "...", -1, true, true, false, ImageLocation.MiddleCenter, null, new KeyShortcut((Keys)131151), appearance, "", null, null, true)
			});
			this.buttonEditFilePath.Properties.ReadOnly = true;
			this.buttonEditFilePath.Size = new Size(632, 20);
			this.buttonEditFilePath.StyleController = this.layoutControl1;
			this.buttonEditFilePath.TabIndex = 0;
			this.buttonEditFilePath.ButtonClick += this.buttonEditFilePath_ButtonClick;
			this.layoutControlGroup1.CustomizationFormText = "layoutControlGroup1";
			this.layoutControlGroup1.EnableIndentsWithoutBorders = DefaultBoolean.True;
			this.layoutControlGroup1.GroupBordersVisible = false;
			this.layoutControlGroup1.Items.AddRange(new BaseLayoutItem[]
			{
				this.lciFilePath,
				this.lciTable,
				this.layoutControlItem2,
				this.emptySpaceItem1,
				this.emptySpaceItem2,
				this.emptySpaceItem3,
				this.layoutControlItemResult,
				this.layoutControlItem3,
				this.layoutControlItem1
			});
			this.layoutControlGroup1.Location = new Point(0, 0);
			this.layoutControlGroup1.Name = "Root";
			this.layoutControlGroup1.OptionsToolTip.ToolTip = "You can drag and drop the xml or dtx file into the form.";
			this.layoutControlGroup1.Padding = new DevExpress.XtraLayout.Utils.Padding(5, 5, 5, 5);
			this.layoutControlGroup1.Size = new Size(988, 693);
			this.layoutControlGroup1.TextVisible = false;
			this.lciFilePath.Control = this.buttonEditFilePath;
			this.lciFilePath.CustomizationFormText = "File";
			this.lciFilePath.Location = new Point(0, 0);
			this.lciFilePath.Name = "lciFilePath";
			this.lciFilePath.Size = new Size(665, 24);
			this.lciFilePath.Text = "File";
			this.lciFilePath.TextSize = new Size(26, 13);
			this.lciTable.Control = this.cbeTable;
			this.lciTable.CustomizationFormText = "Table";
			this.lciTable.Location = new Point(0, 24);
			this.lciTable.Name = "lciTable";
			this.lciTable.Size = new Size(665, 26);
			this.lciTable.Text = "Table";
			this.lciTable.TextSize = new Size(26, 13);
			this.layoutControlItem2.Control = this.sbtnAction;
			this.layoutControlItem2.CustomizationFormText = "layoutControlItem2";
			this.layoutControlItem2.Location = new Point(874, 657);
			this.layoutControlItem2.Name = "layoutControlItem2";
			this.layoutControlItem2.Size = new Size(104, 26);
			this.layoutControlItem2.TextSize = new Size(0, 0);
			this.layoutControlItem2.TextVisible = false;
			this.emptySpaceItem1.AllowHotTrack = false;
			this.emptySpaceItem1.CustomizationFormText = "emptySpaceItem1";
			this.emptySpaceItem1.Location = new Point(0, 657);
			this.emptySpaceItem1.Name = "emptySpaceItem1";
			this.emptySpaceItem1.Size = new Size(874, 26);
			this.emptySpaceItem1.TextSize = new Size(0, 0);
			this.emptySpaceItem2.AllowHotTrack = false;
			this.emptySpaceItem2.CustomizationFormText = "emptySpaceItem2";
			this.emptySpaceItem2.Location = new Point(665, 0);
			this.emptySpaceItem2.Name = "emptySpaceItem2";
			this.emptySpaceItem2.Size = new Size(313, 24);
			this.emptySpaceItem2.TextSize = new Size(0, 0);
			this.emptySpaceItem3.AllowHotTrack = false;
			this.emptySpaceItem3.CustomizationFormText = "emptySpaceItem3";
			this.emptySpaceItem3.Location = new Point(753, 24);
			this.emptySpaceItem3.Name = "emptySpaceItem3";
			this.emptySpaceItem3.Size = new Size(225, 26);
			this.emptySpaceItem3.TextSize = new Size(0, 0);
			this.layoutControlItemResult.Control = this.gridControlResult;
			this.layoutControlItemResult.CustomizationFormText = "layoutControlItemResult";
			this.layoutControlItemResult.Location = new Point(0, 417);
			this.layoutControlItemResult.Name = "layoutControlItemResult";
			this.layoutControlItemResult.Padding = new DevExpress.XtraLayout.Utils.Padding(5, 5, 5, 5);
			this.layoutControlItemResult.Size = new Size(978, 240);
			this.layoutControlItemResult.TextSize = new Size(0, 0);
			this.layoutControlItemResult.TextVisible = false;
			this.layoutControlItemResult.Visibility = LayoutVisibility.Never;
			this.layoutControlItem3.Control = this.gcMain;
			this.layoutControlItem3.CustomizationFormText = "layoutControlItem3";
			this.layoutControlItem3.Location = new Point(0, 50);
			this.layoutControlItem3.Name = "layoutControlItem3";
			this.layoutControlItem3.Size = new Size(978, 367);
			this.layoutControlItem3.TextSize = new Size(0, 0);
			this.layoutControlItem3.TextVisible = false;
			this.layoutControlItem1.Control = this.sbtnExportToExcel;
			this.layoutControlItem1.CustomizationFormText = "layoutControlItem1";
			this.layoutControlItem1.Location = new Point(665, 24);
			this.layoutControlItem1.Name = "layoutControlItem1";
			this.layoutControlItem1.Size = new Size(88, 26);
			this.layoutControlItem1.TextSize = new Size(0, 0);
			this.layoutControlItem1.TextVisible = false;
			this.AllowDrop = true;
			base.AutoScaleDimensions = new SizeF(6f, 13f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.ClientSize = new Size(988, 693);
			base.Controls.Add(this.layoutControl1);
			base.Name = "frmEDIInfoView";
			base.ShowIcon = false;
			this.Text = "Meta Data Viewer";
			base.FormClosed += this.frmEDIInfoView_FormClosed;
			base.DragDrop += this.frmEDIInfoView_DragDrop;
			base.DragEnter += this.frmEDIInfoView_DragEnter;
			((ISupportInitialize)this.layoutControl1).EndInit();
			this.layoutControl1.ResumeLayout(false);
			((ISupportInitialize)this.gcMain).EndInit();
			((ISupportInitialize)this.gvMain).EndInit();
			((ISupportInitialize)this.gridControlResult).EndInit();
			((ISupportInitialize)this.gvResult).EndInit();
			((ISupportInitialize)this.cbeTable.Properties).EndInit();
			((ISupportInitialize)this.buttonEditFilePath.Properties).EndInit();
			((ISupportInitialize)this.layoutControlGroup1).EndInit();
			((ISupportInitialize)this.lciFilePath).EndInit();
			((ISupportInitialize)this.lciTable).EndInit();
			((ISupportInitialize)this.layoutControlItem2).EndInit();
			((ISupportInitialize)this.emptySpaceItem1).EndInit();
			((ISupportInitialize)this.emptySpaceItem2).EndInit();
			((ISupportInitialize)this.emptySpaceItem3).EndInit();
			((ISupportInitialize)this.layoutControlItemResult).EndInit();
			((ISupportInitialize)this.layoutControlItem3).EndInit();
			((ISupportInitialize)this.layoutControlItem1).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x040010A4 RID: 4260
		private DataTableCollection _tables;

		// Token: 0x040010A5 RID: 4261
		private static readonly string _Header = "Header";

		// Token: 0x040010A6 RID: 4262
		private static readonly string _EDIType = "EDIType";

		// Token: 0x040010A7 RID: 4263
		private readonly EDILogBLL _ediLogBll;

		// Token: 0x040010A8 RID: 4264
		private LogWriteDisk _logWriteDisk;

		// Token: 0x040010A9 RID: 4265
		private IContainer components;

		// Token: 0x040010AA RID: 4266
		private OpenFileDialog openFileDialog;

		// Token: 0x040010AB RID: 4267
		private LayoutControl layoutControl1;

		// Token: 0x040010AC RID: 4268
		private LayoutControlGroup layoutControlGroup1;

		// Token: 0x040010AD RID: 4269
		private ButtonEdit buttonEditFilePath;

		// Token: 0x040010AE RID: 4270
		private LayoutControlItem lciFilePath;

		// Token: 0x040010AF RID: 4271
		private ComboBoxEdit cbeTable;

		// Token: 0x040010B0 RID: 4272
		private LayoutControlItem lciTable;

		// Token: 0x040010B1 RID: 4273
		private SimpleButton sbtnAction;

		// Token: 0x040010B2 RID: 4274
		private LayoutControlItem layoutControlItem2;

		// Token: 0x040010B3 RID: 4275
		private EmptySpaceItem emptySpaceItem1;

		// Token: 0x040010B4 RID: 4276
		private EmptySpaceItem emptySpaceItem2;

		// Token: 0x040010B5 RID: 4277
		private EmptySpaceItem emptySpaceItem3;

		// Token: 0x040010B6 RID: 4278
		private GridControl gridControlResult;

		// Token: 0x040010B7 RID: 4279
		private GridView gvResult;

		// Token: 0x040010B8 RID: 4280
		private LayoutControlItem layoutControlItemResult;

		// Token: 0x040010B9 RID: 4281
		private GridControl gcMain;

		// Token: 0x040010BA RID: 4282
		private GridView gvMain;

		// Token: 0x040010BB RID: 4283
		private LayoutControlItem layoutControlItem3;

		// Token: 0x040010BC RID: 4284
		private SimpleButton sbtnExportToExcel;

		// Token: 0x040010BD RID: 4285
		private LayoutControlItem layoutControlItem1;
	}
}
